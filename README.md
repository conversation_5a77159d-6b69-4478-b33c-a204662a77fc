# Queue Manager

A robust, high-performance message queue system built in Go, designed for reliable event processing with support for both sequential and concurrent execution patterns.

## Overview

Queue Manager is a complete message broker solution that provides:

- **Reliable Message Delivery**: Persistent message storage with delivery guarantees
- **Flexible Processing Models**: Support for both sequential (ordered) and concurrent processing
- **Scalable Architecture**: Horizontally scalable with multiple consumer instances
- **Robust Error Handling**: Built-in retry mechanisms with configurable policies
- **Clean Interfaces**: Modular design with well-defined abstractions

The system uses RabbitMQ as the underlying message broker but is designed with abstractions that would allow other implementations.

## Key Features

### Processing Models

#### Sequential Processing

Messages with the same hash key are processed in order, ensuring sequential operations for related events:

- Uses partitioned queues based on consistent hashing
- Guarantees ordered processing within each partition
- Ideal for operations that must maintain sequence (e.g., state changes for the same entity)

#### Concurrent Processing

Messages are processed by multiple workers in parallel:

- Higher throughput for independent operations
- Configurable number of consumers per instance
- Suitable for tasks that don't require ordering

### Reliability Features

- **Persistent Messages**: Survives broker restarts
- **Automatic Reconnection**: Handles connection failures gracefully
- **Dead Letter Queues**: Captures failed messages for inspection
- **Configurable Retries**: Custom retry policies with exponential backoff

## Configuration

The system is configured via a YAML file that defines:

- Worker endpoints
- Queue configurations (mode, partitions, retries)
- Topic-to-queue mappings

Example configuration:

```yaml
workers:
  workers-node:
    url: http://workers:8080/run/:queueName
  workers-go:
    url: http://workers-go:8080/run/:queueName

queues:
  notification:
    mode: concurrent
    max-retries: 3
    consumers-per-instance: 10
    worker: workers-node
    topics:
      - 'message.created'

  user-events:
    mode: sequential
    partitions: 5
    max-retries: 3
    worker: workers-go
    topics:
      - 'user.created'
      - 'user.updated'
      - 'user.deleted'
```

## Architecture

### Components

1. **Producer Service**
   - HTTP API for dispatching events
   - Routes messages to appropriate queues
   - Supports hash-based routing for sequential processing

2. **Consumer Service**
   - Processes messages from configured queues
   - Forwards to worker services via HTTP
   - Manages acknowledgments and retries

3. **Worker Services**
   - Implement business logic for processing messages
   - Receive messages via HTTP endpoints
   - Can be implemented in any language

### Project Structure

```
src/
├── common/               # Shared components
│   ├── config/           # Configuration handling
│   ├── queue/            # Queue interfaces
│   └── rabbitmq/         # RabbitMQ implementation
├── producer/             # Message producer service
│   ├── handlers/         # HTTP handlers
│   └── main.go           # Producer entry point
└── consumer/             # Message consumer service
    ├── service/          # Processing service
    ├── worker/           # HTTP worker client
    └── main.go           # Consumer entry point
```

## Getting Started

### Prerequisites

- Go 1.23 or higher
- RabbitMQ 4.x with management plugin
- Docker and Docker Compose (for containerized deployment)

### Running with Docker Compose

The easiest way to run the entire system is with Docker Compose:

```bash
docker-compose up
```

This will start:
- RabbitMQ with the consistent hash exchange plugin
- Producer service on port 2000
- Multiple consumer instances

### Running Services Individually

#### Producer

```bash
cd src/producer
go run main.go --addr=:2000
```

#### Consumer

```bash
cd src/consumer
go run main.go --config=/path/to/config.yaml
```

## Configuration

### Environment Variables

The following environment variables can be used to configure the services:

- `HTTP_BODY_LIMIT_MB`: Maximum request body size in MB for the producer HTTP server (default: 100)
- `RABBITMQ_HOST`: RabbitMQ host (default: localhost)
- `RABBITMQ_PORT`: RabbitMQ port (default: 5672)
- `RABBITMQ_USER`: RabbitMQ username (default: guest)
- `RABBITMQ_PASS`: RabbitMQ password (default: guest)
- `RABBITMQ_VHOST`: RabbitMQ virtual host (default: /)
- `LOG_LEVEL`: Log level (DEBUG, INFO, WARN, ERROR - default: INFO)

## API Usage

### Dispatch an Event

```bash
curl -X POST http://localhost:8080/dispatch \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "message.created",
    "hashKey": "user123",
    "payload": {
      "id": "msg456",
      "content": "Hello, world!"
    }
  }'
```

- `topic`: Event type that determines which queue(s) will process the message
- `hashKey`: Optional key for sequential processing (e.g., user ID, entity ID)
- `payload`: Event data (can be any valid JSON)

### Health Check

```bash
curl http://localhost:2000/health
```

## Development

### Building from Source

```bash
# Build producer
cd src/producer
go build -o producer

# Build consumer
cd src/consumer
go build -o consumer
```

### Testing

The project includes comprehensive test suites:

```bash
# Run unit tests only
make test

# Run integration tests (requires Docker)
make test-integration

# Run all tests
make test-all

# Generate test coverage report
make coverage
```

Test suites include:

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test components working together with a real RabbitMQ instance
- **API Tests**: Test HTTP endpoints and request validation
- **Load Tests**: Test system performance under load

See the [test README](src/test/README.md) for more details on the testing infrastructure.

### Load Testing

The project includes K6 scripts for load testing:

```bash
k6 run load_tests/k6/dispatch.js
```

## License

This project is licensed under the MIT License - see the LICENSE file for details