package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"regexp"
	"strings"
	"sync"
	"time"

	commonConfig "queue-manager/common/config"

	"github.com/cockroachdb/errors"
	"github.com/rabbitmq/amqp091-go"

	"queue-manager/common/default_connection"
	"queue-manager/common/queue"
	"queue-manager/common/queue_config"
	"queue-manager/common/rabbitmq"
	"queue-manager/common/rabbitmq/consumer"
	"queue-manager/consumer/worker"
)

// JobProcessor handles the processing of jobs consumed from queues
type JobProcessor struct {
	connection                 *rabbitmq.Connection
	config                     *queue_config.Config
	httpWorker                 *worker.HTTPWorker
	sharedConsumersGroups      map[string]queue.ConsumerGroup
	partitionedConsumersGroups map[string]queue.ConsumerGroup
	mutex                      sync.RWMutex
	logger                     *slog.Logger
}

// NewJobProcessor creates a new job processor
func NewJobProcessor(connection *rabbitmq.Connection, cfg *queue_config.Config, httpWorker *worker.HTTPWorker) *JobProcessor {
	logger := slog.With("component", "job-processor")

	return &JobProcessor{
		connection:                 connection,
		config:                     cfg,
		httpWorker:                 httpWorker,
		sharedConsumersGroups:      make(map[string]queue.ConsumerGroup),
		partitionedConsumersGroups: make(map[string]queue.ConsumerGroup),
		logger:                     logger,
	}
}

// StartProcessing starts consumers for all queues defined in the configuration
func (p *JobProcessor) StartProcessing() error {
	p.logger.Info("Starting job processing for all queues")

	channel, err := p.connection.Channel()
	if err != nil {
		return errors.Wrap(err, "failed to create channel")
	}

	// Remove all queues that are not defined in the configuration
	if err := p.removeObsoleteQueues(channel, p.logger); err != nil {
		return fmt.Errorf("failed to remove obsolete queues: %w", err)
	}

	// Process each queue configuration
	for queueName, queueCfg := range p.config.Queues {
		// Check if this is a sequential queue
		if queueCfg.Mode == "sequential" {
			if err := p.startPartitionedQueueConsumer(queueName, queueCfg); err != nil {
				return fmt.Errorf("failed to start consumer for partitioned queue '%s': %w", queueName, err)
			}
		} else {
			if err := p.startSharedQueueConsumer(queueName, queueCfg); err != nil {
				return fmt.Errorf("failed to start consumer for shared queue '%s': %w", queueName, err)
			}
		}
	}

	p.logger.Info("All queue consumers started")
	return nil
}

// startPartitionedQueueConsumer starts a consumer for a partitioned queue
func (p *JobProcessor) startPartitionedQueueConsumer(queueName string, queueCfg *queue_config.Queue) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Check if we already have a consumerGroup for this queue
	if _, exists := p.partitionedConsumersGroups[queueName]; exists {
		p.logger.Debug("Consumer already exists for partitioned queue", "queue", queueName)
		return nil
	}

	p.logger.Info("Creating partitioned queue consumerGroup")

	// Create the message handler
	handler := p.createMessageHandler(queueName)

	println("queueCfg.ProcessingTimeoutSeconds", queueCfg.ProcessingTimeoutSeconds)

	// Create the consumerGroup
	consumerGroup, err := consumer.NewPartitionedQueueConsumerGroup(
		p.connection,
		default_connection.MainExchangeName,
		queueName,
		queueCfg.Topics,
		queueCfg.Partitions,
		handler,
		queueCfg.MaxRetries,
		time.Duration(queueCfg.ProcessingTimeoutSeconds)*time.Second,
	)
	if err != nil {
		return fmt.Errorf("failed to create partitioned queue consumerGroup: %w", err)
	}

	p.logger.Info("Starting partitioned queue consumerGroup...")

	// Start consuming
	if err := consumerGroup.Start(); err != nil {
		return fmt.Errorf("failed to start partitioned queue consumerGroup: %w", err)
	}

	// Store the consumerGroup
	p.partitionedConsumersGroups[queueName] = consumerGroup

	p.logger.Info("Started partitioned queue consumerGroup",
		"queue", queueName,
		"partitions", queueCfg.Partitions)

	return nil
}

// startSharedQueueConsumer starts a consumer for a shared queue
func (p *JobProcessor) startSharedQueueConsumer(queueName string, queueCfg *queue_config.Queue) error {
	p.mutex.Lock()
	defer p.mutex.Unlock()

	// Check if we already have a consumer for this queue
	if _, exists := p.sharedConsumersGroups[queueName]; exists {
		p.logger.Debug("Consumer already exists for shared queue", "queue", queueName)
		return nil
	}

	p.logger.Info("Creating shared queue consumer")

	// Create the message handler
	handler := p.createMessageHandler(queueName)

	println("queueCfg.ProcessingTimeoutSeconds", queueCfg.ProcessingTimeoutSeconds)
	// Create the consumer
	consumerGroup, err := consumer.NewSharedQueueConsumerGroup(
		p.connection,
		default_connection.MainExchangeName,
		queueName,
		queueCfg.Topics,
		handler,
		queueCfg.ConsumersPerInstance,
		queueCfg.PrefetchCountPerConsumer,
		queueCfg.MaxRetries,
		time.Duration(queueCfg.ProcessingTimeoutSeconds)*time.Second,
	)
	if err != nil {
		return fmt.Errorf("failed to create shared queue consumer: %w", err)
	}

	// Start consuming
	if err := consumerGroup.Start(); err != nil {
		return fmt.Errorf("failed to start shared queue consumer: %w", err)
	}

	// Store the consumer
	p.sharedConsumersGroups[queueName] = consumerGroup

	p.logger.Info("Started shared queue consumer",
		"queue", queueName,
		"workers", queueCfg.ConsumersPerInstance)

	return nil
}

// createMessageHandler creates a handler function for processing messages
func (p *JobProcessor) createMessageHandler(queueName string) queue.MessageHandler {
	return func(ctx context.Context, rawMessage []byte) error {
		// Parse the job from the message rawMessage
		var message queue.Message
		err := json.Unmarshal(rawMessage, &message)
		if err != nil {
			p.logger.Error("Failed to parse job from message", "queue", queueName, "error", err)
			return err
		}

		logger := p.logger.With("queue", queueName,
			"topic", message.Topic,
			"hashKey", message.HashKey)

		logger.Debug("Processing job")

		// Process the job using the HTTP worker
		if err := p.httpWorker.ProcessMessage(ctx, queueName, &message, rawMessage); err != nil {
			// Log the error and increment retry count
			logger.Error("Failed to process job", "error", err)
			return err
		}

		logger.Debug("Job processed successfully")

		return nil
	}
}

// StopProcessing stops all consumers
func (p *JobProcessor) StopProcessing() error {
	p.logger.Info("Stopping all consumers")

	// Stop shared consumers
	for name, c := range p.sharedConsumersGroups {
		p.logger.Debug("Stopping shared consumer", "queue", name)
		if err := c.Stop(); err != nil {
			p.logger.Error("Error stopping shared consumer", "queue", name, "error", err)
		}
	}

	// Stop partitioned consumers
	for name, c := range p.partitionedConsumersGroups {
		p.logger.Debug("Stopping partitioned consumer", "queue", name)
		if err := c.Stop(); err != nil {
			p.logger.Error("Error stopping partitioned consumer", "queue", name, "error", err)
		}
	}

	p.logger.Info("All consumers stopped")
	return nil
}

// Close releases all resources
func (p *JobProcessor) Close() error {
	p.logger.Info("Closing job processor")

	// Stop all consumers
	if err := p.StopProcessing(); err != nil {
		p.logger.Error("Error stopping processing", "error", err)
	}

	// Close the HTTP worker
	if err := p.httpWorker.Close(); err != nil {
		p.logger.Error("Error closing HTTP worker", "error", err)
	}

	return nil
}

// removeObsoleteQueues removes queues that are no longer needed
func (p *JobProcessor) removeObsoleteQueues(channel *amqp091.Channel, logger *slog.Logger) error {
	queueLogger := logger.With("operation", "removeObsoleteQueues")

	// Get current partition queues bound to the router exchange
	currentQueues, err := p.getCurrentQueues()
	if err != nil {
		queueLogger.Error("Failed to get current partition queues", "error", err)
		return errors.Wrap(err, "failed to get current partition queues")
	}

	// Build a map of current queue modes based on existing queues
	currentModes := make(map[string]string) // queueBaseName -> mode

	for _, queueName := range currentQueues {
		if strings.HasSuffix(queueName, ".work") {
			// Concurrent mode queue (e.g., "bot.work")
			baseName := strings.TrimSuffix(queueName, ".work")
			currentModes[baseName] = "concurrent"
		} else if strings.Contains(queueName, ".work.") && regexp.MustCompile(`\.work\.\d+$`).MatchString(queueName) {
			// Sequential mode queue (e.g., "bot.work.1")
			baseName := strings.Split(queueName, ".work.")[0]
			currentModes[baseName] = "sequential"
		}
	}

	// Convert desired partition names to a set for easier comparison
	desiredQueuesSet := make(map[string]bool)

	for key, queue := range p.config.Queues {
		if queue.Mode == "sequential" {
			for j := 0; j < queue.Partitions; j++ {
				queueName := fmt.Sprintf("%s.work.%d", key, j+1)
				desiredQueuesSet[queueName] = true
			}
		}

		if queue.Mode == "concurrent" {
			desiredQueuesSet[key+".work"] = true
		}

		desiredQueuesSet[key+".dlq"] = true
		desiredQueuesSet[key+".retry"] = true
		desiredQueuesSet[key+"_router_exchange"] = true
	}

	sequentialQueueRegex := regexp.MustCompile(`\.work\.\d+$`)

	for _, queueName := range currentQueues {
		shouldRemove := false
		removalReason := ""

		baseQueueName := p.extractBaseQueueName(queueName)

		// Check if queue is obsolete (not in desired set)
		if !desiredQueuesSet[queueName] {
			shouldRemove = true
			removalReason = "obsolete"
		}

		// Check if queue belongs to a queue that changed mode
		if !shouldRemove {
			if baseQueueName != "" {
				if configQueue, exists := p.config.Queues[baseQueueName]; exists {
					currentMode, hasCurrent := currentModes[baseQueueName]
					if hasCurrent && currentMode != configQueue.Mode {
						shouldRemove = true
						removalReason = fmt.Sprintf("mode changed from %s to %s", currentMode, configQueue.Mode)
					}
				}
			}
		}

		if shouldRemove {
			routerExchangeName := baseQueueName + "_router_exchange"
			routingKey := queueName

			// For sequential queues (.work.N), use routing key "500"
			if sequentialQueueRegex.MatchString(queueName) {
				routingKey = "500"
			}

			// Check if this is a router exchange (needs different removal method)
			if strings.HasSuffix(queueName, "_router_exchange") {
				queueLogger.Info("Removing router exchange", "exchange", queueName, "reason", removalReason)
				if err := p.removeExchange(channel, queueName, queueLogger); err != nil {
					queueLogger.Error("Failed to remove router exchange", "exchange", queueName, "reason", removalReason, "error", err)
				}
			} else {
				queueLogger.Info("Removing queue", "queue", queueName, "reason", removalReason)
				if err := p.removeQueue(channel, queueName, routerExchangeName, routingKey, queueLogger); err != nil {
					queueLogger.Error("Failed to remove queue", "queue", queueName, "reason", removalReason, "error", err)
				}
			}
		}
	}

	return nil
}

func (p *JobProcessor) removeQueue(channel *amqp091.Channel, queueName string, routerExchangeName string, routingKey string, logger *slog.Logger) error {
	// Determine which exchange to unbind from based on queue type
	var exchangeToUnbind string
	var routingKeyToUnbind string

	if strings.HasSuffix(queueName, ".dlq") {
		// DLQ queues are bound to the main exchange with their own name as routing key
		exchangeToUnbind = default_connection.MainExchangeName
		routingKeyToUnbind = queueName
	} else if strings.HasSuffix(queueName, ".retry") {
		// Retry queues are not bound to any exchange for routing - they only have dead letter configuration
		// Skip unbinding for retry queues as they don't have routing bindings
		logger.Debug("Skipping unbind for retry queue (no routing bindings)")
	} else {
		// Work queues and router exchanges are bound to router exchange
		exchangeToUnbind = routerExchangeName
		routingKeyToUnbind = routingKey
	}

	// Only attempt unbind if we have an exchange to unbind from
	if exchangeToUnbind != "" {
		if err := channel.QueueUnbind(
			queueName,          // queue name
			routingKeyToUnbind, // routing key (same as used in binding)
			exchangeToUnbind,   // exchange
			nil,                // arguments
		); err != nil {
			// If the queue doesn't exist, that's fine - it's already unbound
			errStr := err.Error()
			if strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "404") {
				logger.Debug("Queue does not exist, already unbound")
			} else if strings.Contains(errStr, "channel/connection is not open") || strings.Contains(errStr, "504") {
				logger.Debug("Queue does not exist (channel closed), already unbound")
			} else {
				logger.Error("Failed to unbind queue", "exchange", exchangeToUnbind, "routingKey", routingKeyToUnbind, "error", err)
				return errors.Wrap(err, "failed to unbind queue")
			}
		}
	}

	// Delete the queue
	// ifUnused: false - delete even if there are consumers
	// ifEmpty: false - delete even if there are messages
	_, err := channel.QueueDelete(
		queueName, // queue name
		false,     // ifUnused
		false,     // ifEmpty
		false,     // noWait
	)
	if err != nil {
		// If the queue doesn't exist, that's fine - it's already deleted
		errStr := err.Error()
		if strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "404") {
			logger.Debug("Queue does not exist, already deleted")
			return nil
		}
		// If we get a channel closure error, it likely means the queue doesn't exist
		if strings.Contains(errStr, "channel/connection is not open") || strings.Contains(errStr, "504") {
			logger.Debug("Queue does not exist (channel closed), already deleted")
			return nil
		}
		logger.Error("Failed to delete queue", "error", err)
		return errors.Wrap(err, "failed to delete queue")
	}

	logger.Info("Successfully removed queue")
	return nil
}

func (p *JobProcessor) removeExchange(channel *amqp091.Channel, exchangeName string, logger *slog.Logger) error {
	// Delete the exchange
	// ifUnused: false - delete even if there are bindings
	err := channel.ExchangeDelete(
		exchangeName, // exchange name
		false,        // ifUnused
		false,        // noWait
	)
	if err != nil {
		// If the exchange doesn't exist, that's fine - it's already deleted
		errStr := err.Error()
		if strings.Contains(errStr, "NOT_FOUND") || strings.Contains(errStr, "404") {
			logger.Debug("Exchange does not exist, already deleted")
			return nil
		}
		// If we get a channel closure error, it likely means the exchange doesn't exist
		if strings.Contains(errStr, "channel/connection is not open") || strings.Contains(errStr, "504") {
			logger.Debug("Exchange does not exist (channel closed), already deleted")
			return nil
		}
		logger.Error("Failed to delete exchange", "error", err)
		return errors.Wrap(err, "failed to delete exchange")
	}

	logger.Info("Successfully removed exchange")
	return nil
}

// getCurrentQueues retrieves current queues bound to the router exchange
func (p *JobProcessor) getCurrentQueues() ([]string, error) {
	// Try to get queues via Management API if available
	if queues, err := p.getQueuesViaManagementAPI(); err == nil {
		return queues, nil
	}

	// Fallback: assume no existing queues
	return []string{}, nil
}

// getQueuesViaManagementAPI uses LavinMQ Management API to get current queues and exchanges
func (p *JobProcessor) getQueuesViaManagementAPI() ([]string, error) {
	// Get Management API configuration from environment
	managementHost := commonConfig.GetRabbitMQHost()

	managementPort := commonConfig.GetRabbitMQApiPort()

	username := commonConfig.GetRabbitMQUsername()

	password := commonConfig.GetRabbitMQPassword()

	// Create HTTP client with timeout
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	var allItems []string

	// Get all queues
	queuesURL := fmt.Sprintf("http://%s:%s/api/queues", managementHost, managementPort)
	if queues, err := p.getItemsFromAPI(client, queuesURL, username, password, "queues"); err == nil {
		allItems = append(allItems, queues...)
	}

	// Get all exchanges (filter for router exchanges)
	exchangesURL := fmt.Sprintf("http://%s:%s/api/exchanges", managementHost, managementPort)
	if exchanges, err := p.getItemsFromAPI(client, exchangesURL, username, password, "exchanges"); err == nil {
		// Only include router exchanges (those ending with _router_exchange)
		for _, exchange := range exchanges {
			if strings.HasSuffix(exchange, "_router_exchange") {
				allItems = append(allItems, exchange)
			}
		}
	}

	return allItems, nil
}

// getItemsFromAPI is a helper function to get items from a specific API endpoint
func (p *JobProcessor) getItemsFromAPI(client *http.Client, apiURL, username, password, itemType string) ([]string, error) {
	// Create request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request for %s: %w", itemType, err)
	}

	// Set basic auth
	req.SetBasicAuth(username, password)
	req.Header.Set("Accept", "application/json")

	// Make request
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to call %s API: %w", itemType, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("%s API returned status %d", itemType, resp.StatusCode)
	}

	// Parse response - both queues and exchanges have a "name" field
	var items []map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&items); err != nil {
		return nil, fmt.Errorf("failed to decode %s response: %w", itemType, err)
	}

	// Extract names
	var names []string
	for _, item := range items {
		if name, ok := item["name"].(string); ok {
			names = append(names, name)
		}
	}

	return names, nil
}

// extractBaseQueueName extracts the base queue name from various queue types
func (p *JobProcessor) extractBaseQueueName(queueName string) string {
	// Remove suffixes to get the base queue name
	if strings.Contains(queueName, ".work.") {
		return strings.Split(queueName, ".work.")[0]
	} else if strings.HasSuffix(queueName, ".work") {
		return strings.TrimSuffix(queueName, ".work")
	} else if strings.HasSuffix(queueName, ".dlq") {
		return strings.TrimSuffix(queueName, ".dlq")
	} else if strings.HasSuffix(queueName, ".retry") {
		return strings.TrimSuffix(queueName, ".retry")
	} else if strings.HasSuffix(queueName, "_router_exchange") {
		return strings.TrimSuffix(queueName, "_router_exchange")
	}

	// If no known suffix, return the queue name as is
	return queueName
}
