package topology

import (
	"context"
	"testing"
	"time"

	"github.com/docker/go-connections/nat"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"

	"queue-manager/common/queue"
	"queue-manager/common/rabbitmq"

	amqp "github.com/rabbitmq/amqp091-go"
)

// ExponentialBackoffRetryPolicy implements a simple exponential backoff retry policy for testing
type ExponentialBackoffRetryPolicy struct {
	maxRetries int
	baseMs     int64
	factor     float64
}

func NewExponentialBackoffRetryPolicy(maxRetries int, baseMs int64, factor float64) queue.RetryPolicy {
	return &ExponentialBackoffRetryPolicy{
		maxRetries: maxRetries,
		baseMs:     baseMs,
		factor:     factor,
	}
}

func (e *ExponentialBackoffRetryPolicy) NextTTL(retryCount int) int64 {
	if retryCount >= e.maxRetries {
		return 0
	}

	// Calculate exponential backoff
	ttl := e.baseMs
	for i := 0; i < retryCount; i++ {
		ttl = int64(float64(ttl) * e.factor)
	}
	return ttl
}

func (e *ExponentialBackoffRetryPolicy) GetMaxRetries() int {
	return e.maxRetries
}

// startRabbitmqContainer starts a LavinMQ container for testing (supports x-consistent-hash)
func startRabbitmqContainer(ctx context.Context) (testcontainers.Container, rabbitmq.ConnectionConfig, error) {
	rabbitPort := "5672/tcp"
	req := testcontainers.ContainerRequest{
		Image:        "cloudamqp/lavinmq:latest",
		ExposedPorts: []string{rabbitPort},
		WaitingFor:   wait.ForListeningPort(nat.Port(rabbitPort)),
		Env: map[string]string{
			"LAVINMQ_DEFAULT_USER": "guest",
			"LAVINMQ_DEFAULT_PASS": "guest",
		},
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return nil, rabbitmq.ConnectionConfig{}, err
	}

	// Get the mapped port
	mappedPort, err := container.MappedPort(ctx, nat.Port(rabbitPort))
	if err != nil {
		return nil, rabbitmq.ConnectionConfig{}, err
	}

	// Get the host
	host, err := container.Host(ctx)
	if err != nil {
		return nil, rabbitmq.ConnectionConfig{}, err
	}

	// Create connection config
	connectionConfig := rabbitmq.ConnectionConfig{
		Name:     "integration-test",
		Username: "guest",
		Password: "guest",
		Host:     host,
		Port:     mappedPort.Port(),
		VHost:    "/",
	}

	return container, connectionConfig, nil
}

// TestSetupDLQIntegration tests the SetupDLQ method with a real RabbitMQ instance
func TestSetupDLQIntegration(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Set up test environment
	ctx := context.Background()

	// Start RabbitMQ container
	rabbitmqContainer, connectionConfig, err := startRabbitmqContainer(ctx)
	require.NoError(t, err)
	defer func() {
		if err := rabbitmqContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate container: %v", err)
		}
	}()

	// Create connection
	conn, err := rabbitmq.NewConnection(connectionConfig)
	require.NoError(t, err)
	defer conn.Close()

	// Create channel
	channel, err := conn.Channel()
	require.NoError(t, err)
	defer channel.Close()

	// Create exchange
	exchangeName := "test-exchange"
	err = channel.ExchangeDeclare(
		exchangeName, // name
		"topic",      // type
		true,         // durable
		false,        // auto-deleted
		false,        // internal
		false,        // no-wait
		nil,          // arguments
	)
	require.NoError(t, err)

	// Create a base topology for testing
	topics := []string{"message.created"}
	topology := NewBaseTopology(exchangeName, "notification_work_queue", topics, "notification_work_queue")

	// Call the method under test
	err = topology.SetupDLQ(conn)
	require.NoError(t, err)

	// Verify the DLQ was created
	expectedDLQName := topology.GetDLQName()
	queue, err := channel.QueueDeclarePassive(
		expectedDLQName, // name
		true,            // durable
		false,           // delete when unused
		false,           // exclusive
		false,           // no-wait
		nil,             // arguments
	)
	require.NoError(t, err)
	assert.Equal(t, expectedDLQName, queue.Name)

	// Verify the binding was created by publishing a message to the exchange with the DLQ routing key
	// and then consuming from the DLQ
	err = channel.PublishWithContext(
		ctx,
		exchangeName,    // exchange
		expectedDLQName, // routing key
		false,           // mandatory
		false,           // immediate
		amqp.Publishing{
			ContentType: "text/plain",
			Body:        []byte("test message"),
		},
	)
	require.NoError(t, err)

	// Wait a moment for the message to be routed
	time.Sleep(100 * time.Millisecond)

	// Consume the message from the DLQ
	msgs, err := channel.Consume(
		expectedDLQName, // queue
		"",              // consumer
		true,            // auto-ack
		false,           // exclusive
		false,           // no-local
		false,           // no-wait
		nil,             // args
	)
	require.NoError(t, err)

	// Wait for the message
	select {
	case msg := <-msgs:
		assert.Equal(t, "test message", string(msg.Body))
	case <-time.After(1 * time.Second):
		t.Fatal("Timed out waiting for message")
	}
}

// TestSetupRetryQueuesIntegration tests the SetupRetryQueues method with a real RabbitMQ instance
func TestSetupRetryQueuesIntegration(t *testing.T) {
	// Skip if not running integration tests
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Set up test environment
	ctx := context.Background()

	// Start RabbitMQ container
	rabbitmqContainer, connectionConfig, err := startRabbitmqContainer(ctx)
	require.NoError(t, err)
	defer func() {
		if err := rabbitmqContainer.Terminate(ctx); err != nil {
			t.Logf("Failed to terminate container: %v", err)
		}
	}()

	// Create connection
	conn, err := rabbitmq.NewConnection(connectionConfig)
	require.NoError(t, err)
	defer conn.Close()

	// Create channel
	channel, err := conn.Channel()
	require.NoError(t, err)
	defer channel.Close()

	// Create exchange
	exchangeName := "test-exchange"
	err = channel.ExchangeDeclare(
		exchangeName, // name
		"topic",      // type
		true,         // durable
		false,        // auto-deleted
		false,        // internal
		false,        // no-wait
		nil,          // arguments
	)
	require.NoError(t, err)

	// Create a base topology for testing
	topics := []string{"message.created"}
	topology := NewBaseTopology(exchangeName, "notification_work_queue", topics, "notification_work_queue")

	// Enable multiple retry queues mode for this test
	topology.EnableMultipleRetryQueues()

	// Create a retry policy
	retryPolicy := NewExponentialBackoffRetryPolicy(3, 1000, 2.0)

	// Call the method under test
	err = topology.SetupRetryQueues(conn, retryPolicy)
	require.NoError(t, err)

	// Verify the retry queues were created
	expectedRetryQueues := []string{
		"notification_work_queue.retry.1.0s",
		"notification_work_queue.retry.2.0s",
		"notification_work_queue.retry.4.0s",
	}

	for _, queueName := range expectedRetryQueues {
		// Verify the queue exists
		queue, err := channel.QueueDeclarePassive(
			queueName, // name
			true,      // durable
			false,     // delete when unused
			false,     // exclusive
			false,     // no-wait
			nil,       // arguments
		)
		require.NoError(t, err)
		assert.Equal(t, queueName, queue.Name)
	}

	// Test the retry flow by publishing a message to a retry queue and verifying it gets routed
	// to the work queue after the TTL expires

	// First, declare the work queue so we can consume from it
	_, err = channel.QueueDeclare(
		"notification_work_queue", // name
		true,                      // durable
		false,                     // delete when unused
		false,                     // exclusive
		false,                     // no-wait
		amqp.Table{
			"x-queue-type": "quorum",
		},
	)
	require.NoError(t, err)

	// Bind the work queue to the exchange
	err = channel.QueueBind(
		"notification_work_queue", // queue name
		"notification_work_queue", // routing key
		exchangeName,              // exchange
		false,                     // no-wait
		nil,                       // arguments
	)
	require.NoError(t, err)

	// Publish a message to the shortest TTL retry queue
	err = channel.PublishWithContext(
		ctx,
		"",                                   // exchange (direct to queue)
		"notification_work_queue.retry.1.0s", // routing key
		false,                                // mandatory
		false,                                // immediate
		amqp.Publishing{
			ContentType: "text/plain",
			Body:        []byte("retry test message"),
		},
	)
	require.NoError(t, err)

	// Consume from the work queue
	msgs, err := channel.Consume(
		"notification_work_queue", // queue
		"",                        // consumer
		true,                      // auto-ack
		false,                     // exclusive
		false,                     // no-local
		false,                     // no-wait
		nil,                       // args
	)
	require.NoError(t, err)

	// Wait for the message to be routed after the TTL expires
	select {
	case msg := <-msgs:
		assert.Equal(t, "retry test message", string(msg.Body))
	case <-time.After(2 * time.Second): // Wait a bit longer than the TTL
		t.Fatal("Timed out waiting for message to be routed from retry queue")
	}
}
