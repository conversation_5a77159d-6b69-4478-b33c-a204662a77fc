handlers:
  workers-node:
    url: http://host.docker.internal:8081/run/:queueName
  workers-node-socket:
    url: http://host.docker.internal:8081/run/:queueName
  workers-go:
    url: http://host.docker.internal:8081/run/:queueName

defaults:
  max-retries: 25
  processing-timeout-seconds: 60
  mode: concurrent
  consumers-per-instance: 10
  prefetch-count-per-consumer: 100
  handler: workers-node

queues:
  queued-whatsapp-business-webhook:
    mode: sequential
    partitions: 5
    max-retries: 10
    handler: workers-node
    topics:
      - 'queued-whatsapp-business-webhook'

  notification:
    mode: concurrent
    max-retries: 3
    consumers-per-instance: 100
    handler: workers-node
    topics:
      - 'message.created'

  bot-job:
    mode: sequential
    max-retries: 10
    partitions: 15
    handler: workers-go
    processing-timeout-seconds: 30
    topics:
      - 'message.created'
      - 'message.widget_message_received'
      - 'message.user_message_sent'
      - 'ticket.opened'
      - 'ticket.closed'
      - 'ticket.inactive'
      - 'contact.ANONYMOUS_USER_CREATED_WEBCHAT'
      - 'contact.INIT_CHAT_WEBCHAT'
      - 'contact.created'
      - 'bot.API_SIGNAL'

  socket-sender:
    mode: concurrent
    consumers-per-instance: 10
    handler: workers-node-socket
    topics:
      - 'account.updated'
      - 'contact.created'
      - 'contact.updated'
      - 'contact.destroyed'
      - 'ticket.created'
      - 'ticket.updated'
      - 'ticket.destroyed'
      - 'ticket.opened'
      - 'ticket.transfer'
      - 'ticket.closed'
      - 'service.created'
      - 'service.updated'
      - 'service.destroyed'
      - 'user.updated'
      - 'user.took_over'
      - 'tag.created'
      - 'tag.updated'
      - 'tag.destroyed'
      - 'notifications.created'
      - 'notifications.updated'
      - 'campaign.created'
      - 'campaign.updated'
      - 'campaign.destroyed'